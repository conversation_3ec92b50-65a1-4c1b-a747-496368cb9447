import React, { useState } from 'react';
import { X, Download, Upload, Trash2, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { StorageManager } from '@/utils/storage';
import { useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';

interface SettingsPanelProps {
  onClose: () => void;
}

/**
 * 设置面板组件 - 数据管理（增强版）
 */
const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const [operationStatus, setOperationStatus] = useState<{
    type: 'success' | 'error' | 'loading' | null;
    message: string;
    details?: {
      workspaces?: number;
      websites?: number;
      mappings?: number;
      version?: string;
    };
  }>({ type: null, message: '' });

  // Toast 错误处理
  const { showError, showSuccess } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  /**
   * 显示操作状态（优化版：确保加载状态正确显示）
   */
  const showStatus = (
    type: 'success' | 'error' | 'loading',
    message: string,
    details?: { workspaces?: number; websites?: number; mappings?: number; version?: string }
  ) => {
    // 立即设置状态，确保UI能够响应
    setOperationStatus({ type, message, details });

    // 强制触发重新渲染（确保第一次导入也能显示进度）
    setTimeout(() => {
      setOperationStatus({ type, message, details });
    }, 0);

    // 只有成功和错误状态才自动清除，加载状态需要手动清除
    if (type !== 'loading') {
      setTimeout(() => {
        setOperationStatus({ type: null, message: '' });
      }, type === 'success' ? 5000 : 3000); // 成功消息显示更久
    }
  };

  /**
   * 导出数据（增强版：包含完整数据结构）
   */
  const handleExport = async () => {
    try {
      setOperationStatus({ type: null, message: '正在导出数据...' });

      const result = await StorageManager.exportData();
      if (result.success) {
        const blob = new Blob([result.data!], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workspace-pro-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 解析导出数据以显示摘要
        const exportData = JSON.parse(result.data!);
        const summary = exportData.exportMetadata
          ? `${exportData.exportMetadata.totalWorkspaces} 个工作区, ${exportData.exportMetadata.totalWebsites} 个网站`
          : `${exportData.workspaces?.length || 0} 个工作区`;

        showSuccess(`导出成功！包含 ${summary}`);
      } else {
        errorHandler.handleDataError(result.error, '导出');
      }
    } catch (error) {
      console.error('Failed to export data:', error);
      errorHandler.handleDataError(error, '导出');
    }
  };

  /**
   * 导入数据（优化版：改进用户体验）
   */
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          // 立即显示加载状态，确保第一次导入也能看到进度
          showStatus('loading', '正在读取文件...');

          const text = await file.text();

          // 显示解析状态
          showStatus('loading', '正在解析数据格式...');

          // 预验证数据格式
          let importData: any;
          let workspaceCount: number;
          let websiteCount: number;
          let mappingCount: number;
          let version: string;

          try {
            importData = JSON.parse(text);
            if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
              // 数据格式无效，重置状态并显示错误
              setOperationStatus({ type: null, message: '' });
              errorHandler.handle('无效的数据格式：缺少工作区数据');
              return;
            }

            // 计算统计信息
            workspaceCount = importData.workspaces.length;
            websiteCount = importData.workspaces.reduce((sum: number, ws: any) => sum + (ws.websites?.length || 0), 0);
            mappingCount = importData.tabIdMappings?.length || 0;
            version = importData.version || '1.0.0';

            // 显示详细的导入预览
            const confirmMessage = `确定要导入以下数据吗？\n\n` +
              `📊 数据统计：\n` +
              `• 数据版本: ${version}\n` +
              `• 工作区数量: ${workspaceCount}\n` +
              `• 网站数量: ${websiteCount}\n` +
              `• 标签页映射: ${mappingCount}\n\n` +
              `⚠️ 注意：将进行增量导入，重复的工作区、网站URL将被跳过，现有数据不会丢失`;

            if (!confirm(confirmMessage)) {
              // 用户取消，清除状态
              setOperationStatus({ type: null, message: '' });
              return;
            }
          } catch (parseError) {
            // 解析失败，重置状态并显示错误
            setOperationStatus({ type: null, message: '' });
            errorHandler.handle('无效的JSON文件格式');
            return;
          }

          // 显示导入进行状态（确保显示进度）
          showStatus('loading', '正在导入数据，请稍候...', {
            workspaces: workspaceCount,
            websites: websiteCount,
            mappings: mappingCount,
            version: version
          });

          const result = await StorageManager.importData(text);
          if (result.success) {
            // 导入成功：直接关闭面板并刷新页面，不显示成功消息
            console.log(`✅ 数据导入成功: ${workspaceCount} 个工作区, ${websiteCount} 个网站`);

            // 立即关闭面板
            onClose();

            // 短暂延迟后刷新页面
            setTimeout(() => {
              window.location.reload();
            }, 100);
          } else {
            // 导入失败：重置状态并使用 Toast 显示错误
            setOperationStatus({ type: null, message: '' });
            errorHandler.handleDataError(result.error, '导入');
          }
        } catch (error) {
          console.error('Failed to import data:', error);
          // 导入异常：重置状态并使用 Toast 显示错误
          setOperationStatus({ type: null, message: '' });
          errorHandler.handleDataError(error, '导入');
        }
      }
    };
    input.click();
  };

  /**
   * 清除所有数据（增强版：包含确认和反馈）
   */
  const handleClearAll = async () => {
    const confirmMessage = '⚠️ 危险操作确认\n\n' +
      '这将永久删除以下所有数据：\n' +
      '• 所有工作区和网站\n' +
      '• 工作区会话和标签页状态\n' +
      '• 标签页ID映射\n' +
      '• 用户设置\n\n' +
      '此操作无法撤销！\n\n' +
      '确定要继续吗？';

    if (confirm(confirmMessage)) {
      try {
        setOperationStatus({ type: null, message: '正在清除所有数据...' });

        const result = await StorageManager.clearAll();
        if (result.success) {
          showSuccess('所有数据已清除！页面即将刷新...');
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          errorHandler.handleDataError(result.error, '清除');
        }
      } catch (error) {
        console.error('Failed to clear data:', error);
        errorHandler.handleDataError(error, '清除');
      }
    }
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in max-w-md">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            数据管理
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 操作状态显示 */}
        {operationStatus.message && (
          <div className={`p-3 rounded-lg mb-4 ${
            operationStatus.type === 'success'
              ? 'bg-green-900/50 border border-green-700 text-green-300'
              : operationStatus.type === 'error'
              ? 'bg-red-900/50 border border-red-700 text-red-300'
              : operationStatus.type === 'loading'
              ? 'bg-blue-900/50 border border-blue-700 text-blue-300'
              : 'bg-slate-900/50 border border-slate-700 text-slate-300'
          }`}>
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 mt-0.5">
                {operationStatus.type === 'success' && <CheckCircle className="w-4 h-4" />}
                {operationStatus.type === 'error' && <AlertCircle className="w-4 h-4" />}
                {operationStatus.type === 'loading' && <Loader2 className="w-4 h-4 animate-spin" />}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm whitespace-pre-line">{operationStatus.message}</div>
                {/* 显示详细统计信息 */}
                {operationStatus.details && (
                  <div className="mt-2 text-xs opacity-80">
                    {operationStatus.details.version && (
                      <div>版本: {operationStatus.details.version}</div>
                    )}
                    {operationStatus.details.workspaces !== undefined && (
                      <div>工作区: {operationStatus.details.workspaces}</div>
                    )}
                    {operationStatus.details.websites !== undefined && (
                      <div>网站: {operationStatus.details.websites}</div>
                    )}
                    {operationStatus.details.mappings !== undefined && operationStatus.details.mappings > 0 && (
                      <div>映射: {operationStatus.details.mappings}</div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 数据管理内容 */}
        <div className="space-y-4">
          <button
            onClick={handleExport}
            className="w-full btn-secondary justify-center"
            disabled={operationStatus.type === 'loading'}
          >
            <Download className="w-4 h-4" />
            导出数据
          </button>
          <button
            onClick={handleImport}
            className="w-full btn-secondary justify-center"
            disabled={operationStatus.type === 'loading'}
          >
            <Upload className="w-4 h-4" />
            导入数据
          </button>
          <button
            onClick={handleClearAll}
            className="w-full btn-danger justify-center"
            disabled={operationStatus.type === 'loading'}
          >
            <Trash2 className="w-4 h-4" />
            清除所有数据
          </button>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end mt-6 pt-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;
