import {
  WorkspaceSession,
  WorkspaceTab,
  OperationResult,
  SessionSwitchOptions
} from '@/types/workspace';
import { StorageManager } from './storage';
import { WorkonaTabManager } from './workonaTabManager';
import { WorkspaceStateSync } from './workspaceStateSync';
import { ERROR_CODES } from './constants';

/**
 * 工作区会话隔离管理器
 * 基于现有的 WorkspaceStateSync，实现 Workona 风格的会话状态管理
 */
export class WorkspaceSessionManager {
  private static currentSession: WorkspaceSession | null = null;
  private static isSessionSwitching = false;

  /**
   * 获取当前会话
   */
  static getCurrentSession(): WorkspaceSession | null {
    return this.currentSession;
  }

  /**
   * 切换工作区会话
   * 复用现有的 WorkspaceStateSync 事件系统
   */
  static async switchSession(
    workspaceId: string,
    options: SessionSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      if (this.isSessionSwitching) {
        console.log('⏳ 会话切换正在进行中，跳过重复请求');
        return { success: true };
      }

      this.isSessionSwitching = true;
      console.log(`🔄 开始切换到工作区会话: ${workspaceId}`);

      // 1. 保存当前会话状态（如果需要）
      if (this.currentSession && options.preserveCurrentSession !== false) {
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          console.warn('保存当前会话失败:', saveResult.error);
        }
      }

      // 2. 加载目标工作区会话
      const loadResult = await this.loadSession(workspaceId);
      if (!loadResult.success) {
        this.isSessionSwitching = false;
        return { success: false, error: loadResult.error };
      }

      this.currentSession = loadResult.data!;

      // 3. 使用现有的 WorkspaceStateSync 发送状态更新事件
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, 'switch');

      // 4. 如果需要，恢复标签页顺序和活跃状态
      if (options.restoreTabOrder !== false && this.currentSession.tabOrder.length > 0) {
        await this.restoreTabOrder(this.currentSession);
      }

      // 5. 智能激活标签页：优先恢复保存的活跃标签页，否则激活工作区核心标签页
      if (!this.currentSession.activeTabId && this.currentSession.tabOrder.length > 0) {
        console.log('📋 没有保存的活跃标签页，智能选择合适的标签页激活');

        // 优先选择工作区核心标签页
        let targetTabId: string | null = null;

        for (const workonaId of this.currentSession.tabOrder) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
            targetTabId = workonaId;
            console.log(`🎯 选择工作区核心标签页: ${workonaId}`);
            break;
          }
        }

        // 如果没有工作区核心标签页，选择第一个标签页
        if (!targetTabId && this.currentSession.tabOrder.length > 0) {
          targetTabId = this.currentSession.tabOrder[0];
          console.log(`🎯 选择第一个标签页: ${targetTabId}`);
        }

        // 激活选中的标签页
        if (targetTabId) {
          const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(targetTabId);
          if (chromeIdResult.success && chromeIdResult.data) {
            try {
              await chrome.tabs.update(chromeIdResult.data, { active: true });
              console.log(`✨ 智能激活标签页: ${targetTabId}`);

              // 更新会话状态
              this.currentSession.activeTabId = targetTabId;
              await this.saveSession(this.currentSession);
            } catch (error) {
              console.warn('智能激活标签页失败:', error);
            }
          }
        }
      }

      console.log(`✅ 成功切换到工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace session',
          details: error,
        },
      };
    } finally {
      this.isSessionSwitching = false;
    }
  }

  /**
   * 获取当前工作区的标签页
   * 确保会话只包含当前工作区的标签页
   */
  static getCurrentWorkspaceTabs(): WorkspaceTab[] {
    if (!this.currentSession) {
      return [];
    }

    return Object.values(this.currentSession.tabs);
  }

  /**
   * 获取当前工作区的标签页顺序
   */
  static getCurrentTabOrder(): string[] {
    if (!this.currentSession) {
      return [];
    }

    return [...this.currentSession.tabOrder];
  }

  /**
   * 更新会话中的标签页
   */
  static async updateSessionTab(workonaId: string, tab: WorkspaceTab): Promise<OperationResult<void>> {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active session',
          },
        };
      }

      // 更新标签页信息，保持现有的位置和状态
      const existingTab = this.currentSession.tabs[workonaId];
      this.currentSession.tabs[workonaId] = {
        ...existingTab, // 保持现有属性
        ...tab // 应用更新
      };
      this.currentSession.lastActiveAt = Date.now();

      // 如果标签页不在顺序中，添加到末尾
      if (!this.currentSession.tabOrder.includes(workonaId)) {
        this.currentSession.tabOrder.push(workonaId);
        console.log(`📝 添加新标签页到会话顺序: ${workonaId}`);
      } else {
        console.log(`🔄 更新现有标签页会话信息: ${workonaId}`);
      }

      // 保存会话
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to update session tab',
          details: error,
        },
      };
    }
  }

  /**
   * 从会话中移除标签页
   */
  static async removeSessionTab(workonaId: string): Promise<OperationResult<void>> {
    try {
      if (!this.currentSession) {
        return { success: true }; // 没有活跃会话，认为操作成功
      }

      // 从标签页映射中移除
      delete this.currentSession.tabs[workonaId];

      // 从顺序中移除
      this.currentSession.tabOrder = this.currentSession.tabOrder.filter(id => id !== workonaId);

      // 如果移除的是当前激活的标签页，清除激活状态
      if (this.currentSession.activeTabId === workonaId) {
        this.currentSession.activeTabId = undefined;
      }

      this.currentSession.lastActiveAt = Date.now();

      // 保存会话
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`🗑️ 从会话中移除标签页: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to remove session tab',
          details: error,
        },
      };
    }
  }

  /**
   * 设置当前激活的标签页
   */
  static async setActiveTab(workonaId: string): Promise<OperationResult<void>> {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active session',
          },
        };
      }

      this.currentSession.activeTabId = workonaId;
      this.currentSession.lastActiveAt = Date.now();

      // 更新标签页的最后激活时间
      if (this.currentSession.tabs[workonaId]) {
        this.currentSession.tabs[workonaId].lastActiveAt = Date.now();
      }

      // 保存会话
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`📍 记录活跃标签页: ${workonaId} (工作区: ${this.currentSession.workspaceId})`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to set active tab',
          details: error,
        },
      };
    }
  }

  /**
   * 实时同步当前工作区的标签页状态
   * 记录标签页顺序和活跃状态
   */
  static async syncCurrentWorkspaceState(): Promise<OperationResult<void>> {
    try {
      if (!this.currentSession) {
        return { success: true }; // 没有活跃会话，跳过同步
      }

      console.log(`🔄 同步工作区标签页状态: ${this.currentSession.workspaceId}`);

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 获取当前工作区的Workona标签页
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(this.currentSession.workspaceId);
      if (!workonaTabIds.success) {
        return { success: false, error: workonaTabIds.error };
      }

      const currentWorkspaceTabs: { workonaId: string; chromeTab: chrome.tabs.Tab; index: number }[] = [];
      let activeWorkonaId: string | undefined;

      // 匹配Workona标签页与Chrome标签页
      for (const workonaId of workonaTabIds.data!) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find(tab => tab.id === chromeIdResult.data);
          if (chromeTab) {
            currentWorkspaceTabs.push({
              workonaId,
              chromeTab,
              index: chromeTab.index
            });

            // 记录当前活跃的标签页
            if (chromeTab.active) {
              activeWorkonaId = workonaId;
            }
          }
        }
      }

      // 按Chrome标签页的index排序，得到实际的标签页顺序
      currentWorkspaceTabs.sort((a, b) => a.index - b.index);
      const newTabOrder = currentWorkspaceTabs.map(item => item.workonaId);

      // 更新会话状态
      let hasChanges = false;

      // 检查标签页顺序是否发生变化
      if (JSON.stringify(this.currentSession.tabOrder) !== JSON.stringify(newTabOrder)) {
        this.currentSession.tabOrder = newTabOrder;
        hasChanges = true;
        console.log(`📋 更新标签页顺序: [${newTabOrder.join(', ')}]`);
      }

      // 检查活跃标签页是否发生变化
      if (activeWorkonaId && this.currentSession.activeTabId !== activeWorkonaId) {
        this.currentSession.activeTabId = activeWorkonaId;
        hasChanges = true;

        // 检查是否是工作区专用标签页
        const metadataResult = await WorkonaTabManager.getTabMetadata(activeWorkonaId);
        const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;

        console.log(`📍 更新活跃标签页: ${activeWorkonaId} ${isWorkspaceCore ? '(工作区专用)' : '(用户标签页)'}`);
      }

      // 如果有变化，保存会话
      if (hasChanges) {
        this.currentSession.lastActiveAt = Date.now();
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to sync workspace state',
          details: error,
        },
      };
    }
  }

  /**
   * 更新标签页顺序
   */
  static async updateTabOrder(newOrder: string[]): Promise<OperationResult<void>> {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active session',
          },
        };
      }

      // 验证新顺序中的所有标签页都存在于会话中
      const validOrder = newOrder.filter(workonaId => this.currentSession!.tabs[workonaId]);

      this.currentSession.tabOrder = validOrder;
      this.currentSession.lastActiveAt = Date.now();

      // 保存会话
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`📋 更新标签页顺序: ${validOrder.length} 个标签页`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to update tab order',
          details: error,
        },
      };
    }
  }

  /**
   * 清除当前会话状态
   * 用于浏览器重启后重置工作区状态
   */
  static async clearCurrentSession(): Promise<OperationResult<void>> {
    try {
      console.log('🔄 清除当前工作区会话状态');

      // 清除内存中的当前会话
      this.currentSession = null;
      this.isSessionSwitching = false;

      console.log('✅ 工作区会话状态已清除');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to clear current session',
          details: error,
        },
      };
    }
  }

  /**
   * 将新创建的标签页添加到当前会话
   * 用于后台标签页创建完成后的增量更新
   */
  static async addNewTabsToCurrentSession(newWorkonaIds: string[]): Promise<OperationResult<void>> {
    try {
      if (!this.currentSession) {
        console.log('⚠️ 没有活跃会话，跳过添加新标签页');
        return { success: true };
      }

      if (newWorkonaIds.length === 0) {
        return { success: true };
      }

      console.log(`📝 添加 ${newWorkonaIds.length} 个新标签页到当前会话`);

      // 将新标签页添加到会话顺序末尾（如果不存在的话）
      let addedCount = 0;
      for (const workonaId of newWorkonaIds) {
        if (!this.currentSession.tabOrder.includes(workonaId)) {
          this.currentSession.tabOrder.push(workonaId);
          addedCount++;
        }
      }

      if (addedCount > 0) {
        this.currentSession.lastActiveAt = Date.now();

        // 保存更新后的会话
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }

        console.log(`✅ 成功添加 ${addedCount} 个新标签页到会话顺序`);
      } else {
        console.log('ℹ️ 所有新标签页都已存在于会话中');
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add new tabs to current session',
          details: error,
        },
      };
    }
  }

  /**
   * 保存会话到存储
   */
  private static async saveSession(session: WorkspaceSession): Promise<OperationResult<void>> {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }

      const sessions = sessionsResult.data!;
      sessions[session.workspaceId] = session;

      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save session',
          details: error,
        },
      };
    }
  }

  /**
   * 从存储加载会话
   */
  private static async loadSession(workspaceId: string): Promise<OperationResult<WorkspaceSession>> {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }

      const sessions = sessionsResult.data!;
      let session = sessions[workspaceId];

      // 如果会话不存在，创建新会话
      if (!session) {
        session = {
          workspaceId,
          tabs: {},
          tabOrder: [],
          lastActiveAt: Date.now(),
        };
        
        // 保存新会话
        sessions[workspaceId] = session;
        const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        
        console.log(`✨ 创建新的工作区会话: ${workspaceId}`);
      }

      return { success: true, data: session };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to load session',
          details: error,
        },
      };
    }
  }

  /**
   * 恢复标签页顺序和活跃状态
   */
  private static async restoreTabOrder(session: WorkspaceSession): Promise<void> {
    try {
      console.log(`🔄 恢复工作区状态: ${session.tabOrder.length} 个标签页`);

      if (session.tabOrder.length === 0) {
        console.log('📋 没有保存的标签页顺序，跳过恢复');
        return;
      }

      // 获取当前窗口
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 收集需要重新排序的标签页信息
      const tabsToReorder: { workonaId: string; chromeId: number; targetIndex: number }[] = [];

      for (let i = 0; i < session.tabOrder.length; i++) {
        const workonaId = session.tabOrder[i];
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);

        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find(tab => tab.id === chromeIdResult.data);
          if (chromeTab) {
            tabsToReorder.push({
              workonaId,
              chromeId: chromeTab.id!,
              targetIndex: i
            });
          }
        }
      }

      // 先确定要激活的标签页，避免在移动过程中激活错误的标签页
      let targetActiveTabId: number | null = null;
      if (session.activeTabId) {
        const activeTabResult = await WorkonaTabManager.getChromeIdByWorkonaId(session.activeTabId);
        if (activeTabResult.success && activeTabResult.data) {
          const targetTab = allTabs.find(tab => tab.id === activeTabResult.data);
          if (targetTab) {
            targetActiveTabId = activeTabResult.data;
            console.log(`🎯 确定目标活跃标签页: ${session.activeTabId} (Chrome ID: ${targetActiveTabId})`);
          }
        }
      }

      // 按目标位置排序标签页（批量操作，减少中间状态）
      console.log(`📋 重新排列 ${tabsToReorder.length} 个标签页`);

      // 使用批量移动减少中间状态
      const movePromises = tabsToReorder.map(async (tabInfo) => {
        try {
          await chrome.tabs.move(tabInfo.chromeId, { index: tabInfo.targetIndex });
          console.log(`📍 移动标签页 ${tabInfo.workonaId} 到位置 ${tabInfo.targetIndex}`);
        } catch (error) {
          console.warn(`⚠️ 移动标签页 ${tabInfo.workonaId} 失败:`, error);
        }
      });

      // 等待所有移动操作完成
      await Promise.all(movePromises);
      console.log('✅ 所有标签页移动操作完成');

      // 立即激活目标标签页（无延迟，避免闪烁）
      if (targetActiveTabId) {
        try {
          // 检查是否是工作区专用标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(session.activeTabId!);
          const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;

          // 立即激活目标标签页
          await chrome.tabs.update(targetActiveTabId, { active: true });
          console.log(`✨ 立即激活目标标签页: ${session.activeTabId} ${isWorkspaceCore ? '(工作区专用)' : '(用户标签页)'}`);

          // 确保窗口获得焦点
          const currentWindow = await chrome.windows.getCurrent();
          if (currentWindow.id) {
            chrome.windows.update(currentWindow.id, { focused: true });
          }

          console.log(`🎯 工作区标签页状态恢复完成，无闪烁切换`);
        } catch (error) {
          console.warn(`⚠️ 激活目标标签页失败:`, error);
        }
      } else {
        console.log('📋 没有保存的活跃标签页，智能选择合适的标签页');

        // 智能选择一个合适的标签页激活，避免停留在新标签页
        if (session.tabOrder.length > 0) {
          // 优先选择工作区核心标签页
          let bestTabId: string | null = null;

          for (const workonaId of session.tabOrder) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
              if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
                bestTabId = workonaId;
                break;
              }

              // 如果还没有选择，至少记录第一个可用的标签页
              if (!bestTabId) {
                bestTabId = workonaId;
              }
            }
          }

          // 激活选中的标签页
          if (bestTabId) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(bestTabId);
            if (chromeIdResult.success && chromeIdResult.data) {
              try {
                await chrome.tabs.update(chromeIdResult.data, { active: true });
                console.log(`✨ 智能激活标签页: ${bestTabId}`);
              } catch (error) {
                console.warn('智能激活标签页失败:', error);
              }
            }
          }
        }
      }

      console.log('✅ 工作区状态恢复完成');
    } catch (error) {
      console.warn('恢复工作区状态失败:', error);
    }
  }

  /**
   * 清理工作区会话
   */
  static async clearWorkspaceSession(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }

      const sessions = sessionsResult.data!;
      delete sessions[workspaceId];

      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      // 如果清理的是当前会话，重置当前会话
      if (this.currentSession && this.currentSession.workspaceId === workspaceId) {
        this.currentSession = null;
      }

      console.log(`🗑️ 清理工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear workspace session',
          details: error,
        },
      };
    }
  }
}
