import { WorkSpace, TabIdMapping, WorkspaceSession } from '@/types/workspace';
import { StorageManager } from './storage';
import { WorkonaTabManager } from './workonaTabManager';
import { ERROR_CODES } from './constants';
import { OperationResult } from '@/types/workspace';

/**
 * 导入数据处理器
 * 负责导入数据后的系统映射自动补全
 */
export class ImportDataProcessor {
  /**
   * 处理导入数据后的自动映射补全
   */
  static async processImportedData(importData: any): Promise<OperationResult<void>> {
    try {
      console.log('🔄 开始处理导入数据的系统映射补全...');

      // 1. 补全工作区的 Workona 风格字段
      await this.completeWorkspaceWorkonaFields(importData.workspaces);

      // 2. 生成标签页ID映射
      await this.generateTabIdMappings(importData.workspaces);

      // 3. 生成工作区会话数据
      await this.generateWorkspaceSessions(importData.workspaces);

      // 4. 初始化其他系统映射
      await this.initializeSystemMappings();

      console.log('✅ 导入数据系统映射补全完成');
      return { success: true };
    } catch (error) {
      console.error('❌ 导入数据处理失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to process imported data',
          details: error,
        },
      };
    }
  }

  /**
   * 补全工作区的 Workona 风格字段
   */
  private static async completeWorkspaceWorkonaFields(workspaces: WorkSpace[]): Promise<void> {
    console.log('📝 补全工作区 Workona 风格字段...');

    const updatedWorkspaces = workspaces.map((workspace, index) => {
      // 确保工作区有必要的 Workona 字段
      const updatedWorkspace: WorkSpace = {
        ...workspace,
        // 如果缺少 Workona 字段，自动补全
        type: workspace.type || 'saved',
        pos: workspace.pos || Date.now() + index,
        state: workspace.state || 'inactive',
        workonaTabIds: workspace.workonaTabIds || [],
        sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tabOrder: workspace.tabOrder || [],
        isActive: false, // 导入后所有工作区都不激活
      };

      // 为每个网站生成 workonaTabIds（如果不存在）
      if (updatedWorkspace.workonaTabIds.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.workonaTabIds = updatedWorkspace.websites.map(website => 
          `t-${updatedWorkspace.id}-${website.id}`
        );
      }

      // 生成 tabOrder（如果不存在）
      if (updatedWorkspace.tabOrder.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.tabOrder = updatedWorkspace.websites.map(website => website.id);
      }

      return updatedWorkspace;
    });

    // 保存更新后的工作区
    await StorageManager.saveWorkspaces(updatedWorkspaces);
    console.log(`✅ 已补全 ${updatedWorkspaces.length} 个工作区的 Workona 字段`);
  }

  /**
   * 生成标签页ID映射
   */
  private static async generateTabIdMappings(workspaces: WorkSpace[]): Promise<void> {
    console.log('🔗 生成标签页ID映射...');

    const tabIdMappings: TabIdMapping[] = [];

    for (const workspace of workspaces) {
      for (const website of workspace.websites) {
        const workonaId = `t-${workspace.id}-${website.id}`;
        
        // 创建标签页映射
        const mapping: TabIdMapping = {
          workonaId,
          chromeId: null, // 导入时没有实际的 Chrome 标签页ID
          workspaceId: workspace.id,
          websiteId: website.id,
          isWorkspaceCore: true, // 工作区网站默认为核心标签页
          createdAt: Date.now(),
          metadata: {
            source: 'workspace_website',
            addedToWorkspaceAt: Date.now(),
            isPinned: website.isPinned || false,
            pinnedAt: website.isPinned ? Date.now() : undefined,
          }
        };

        tabIdMappings.push(mapping);
      }
    }

    // 保存标签页映射
    await StorageManager.saveTabIdMappings(tabIdMappings);
    console.log(`✅ 已生成 ${tabIdMappings.length} 个标签页ID映射`);
  }

  /**
   * 生成工作区会话数据
   */
  private static async generateWorkspaceSessions(workspaces: WorkSpace[]): Promise<void> {
    console.log('📊 生成工作区会话数据...');

    const workspaceSessions: Record<string, WorkspaceSession> = {};

    for (const workspace of workspaces) {
      const session: WorkspaceSession = {
        workspaceId: workspace.id,
        sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: Date.now(),
        lastAccessedAt: Date.now(),
        tabOrder: workspace.tabOrder || workspace.websites.map(w => w.id),
        activeTabId: workspace.websites.length > 0 ? workspace.websites[0].id : null,
        windowId: null, // 导入时没有实际的窗口ID
        isActive: false,
        metadata: {
          totalTabs: workspace.websites.length,
          coreTabsCount: workspace.websites.length,
          sessionTabsCount: 0,
          lastSwitchedAt: Date.now(),
          source: 'imported_data'
        }
      };

      workspaceSessions[workspace.id] = session;
    }

    // 保存工作区会话
    await StorageManager.saveWorkspaceSessions(workspaceSessions);
    console.log(`✅ 已生成 ${Object.keys(workspaceSessions).length} 个工作区会话`);
  }

  /**
   * 初始化其他系统映射
   */
  private static async initializeSystemMappings(): Promise<void> {
    console.log('🔧 初始化其他系统映射...');

    // 初始化本地打开工作区数据
    await StorageManager.saveLocalOpenWorkspaces({});

    // 初始化标签组数据
    await StorageManager.saveTabGroups({});

    // 清除全局工作区窗口ID（导入后重新分配）
    await StorageManager.clearGlobalWorkspaceWindowId();

    // 更新数据版本
    await StorageManager.saveDataVersion('2.0.0');

    console.log('✅ 系统映射初始化完成');
  }

  /**
   * 验证导入数据的完整性
   */
  static validateImportedData(importData: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查基本结构
    if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
      errors.push('缺少工作区数据或格式错误');
    }

    // 检查工作区数据
    if (importData.workspaces) {
      for (let i = 0; i < importData.workspaces.length; i++) {
        const workspace = importData.workspaces[i];

        if (!workspace.id) {
          errors.push(`工作区 ${i + 1} 缺少ID`);
        }

        if (!workspace.name) {
          errors.push(`工作区 ${i + 1} 缺少名称`);
        }

        if (!workspace.websites || !Array.isArray(workspace.websites)) {
          errors.push(`工作区 ${i + 1} 缺少网站数据或格式错误`);
        } else {
          // 检查网站数据
          for (let j = 0; j < workspace.websites.length; j++) {
            const website = workspace.websites[j];

            if (!website.id) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少ID`);
            }

            if (!website.url) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少URL`);
            }
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 清理导入数据中的无效字段
   */
  static cleanImportData(importData: any): any {
    const cleanedData = { ...importData };

    // 清理工作区数据
    if (cleanedData.workspaces) {
      cleanedData.workspaces = cleanedData.workspaces.map((workspace: any) => ({
        ...workspace,
        isActive: false, // 确保导入后所有工作区都不激活
        // 移除可能导致冲突的字段
        windowId: undefined,
        lastActiveAt: undefined,
      }));
    }

    // 清理可能导致冲突的全局状态
    cleanedData.activeWorkspaceId = null;
    cleanedData.globalWorkspaceWindowId = null;

    return cleanedData;
  }

}
