import {
  OperationResult,
  TabInfo
} from '@/types/workspace';
import { ERROR_CODES } from './constants';
import { StorageManager } from './storage';
import { WorkonaTabManager } from './workonaTabManager';

/**
 * 窗口信息接口
 */
export interface WindowInfo {
  id: number;
  workspaceId: string;
  workspaceName: string;
  tabCount: number;
  isVisible: boolean;
}

/**
 * 专用窗口管理类
 * 实现全局单例专用窗口架构 - 所有工作区共享一个专用窗口
 */
export class WindowManager {
  private static globalWorkspaceWindowId: number | null = null; // 全局专用窗口ID
  private static readonly GLOBAL_WORKSPACE_WINDOW_KEY = 'global_workspace_window';
  private static isCreatingWindow = false; // 防止并发创建窗口

  /**
   * 从存储中恢复全局窗口ID
   */
  private static async loadGlobalWindowId(): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      if (result[this.GLOBAL_WORKSPACE_WINDOW_KEY]) {
        this.globalWorkspaceWindowId = result[this.GLOBAL_WORKSPACE_WINDOW_KEY];
      }
    } catch (error) {
      console.warn('加载全局窗口ID失败:', error);
    }
  }

  /**
   * 保存全局窗口ID到存储
   */
  private static async saveGlobalWindowId(windowId: number): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.GLOBAL_WORKSPACE_WINDOW_KEY]: windowId
      });
      this.globalWorkspaceWindowId = windowId;
    } catch (error) {
      console.warn('保存全局窗口ID失败:', error);
    }
  }

  /**
   * 清理全局窗口ID
   */
  private static async clearGlobalWindowId(): Promise<void> {
    try {
      await chrome.storage.local.remove([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      this.globalWorkspaceWindowId = null;
    } catch (error) {
      console.warn('清理全局窗口ID失败:', error);
    }
  }

  /**
   * 获取或创建全局专用窗口（单例模式）
   */
  static async getOrCreateGlobalWorkspaceWindow(): Promise<OperationResult<WindowInfo>> {
    try {
      console.log('🪟 获取或创建全局工作区专用窗口');

      // 防止并发创建
      if (this.isCreatingWindow) {
        console.log('⏳ 正在创建窗口中，等待完成...');
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
        return await this.getOrCreateGlobalWorkspaceWindow();
      }

      // 从存储中恢复窗口ID（如果内存中没有）
      if (!this.globalWorkspaceWindowId) {
        await this.loadGlobalWindowId();
      }

      // 检查是否已存在全局专用窗口
      if (this.globalWorkspaceWindowId) {
        try {
          // 验证窗口是否仍然存在
          const window = await chrome.windows.get(this.globalWorkspaceWindowId);
          if (window) {
            console.log(`✅ 全局工作区专用窗口已存在: ${this.globalWorkspaceWindowId}`);
            return {
              success: true,
              data: {
                id: this.globalWorkspaceWindowId,
                workspaceId: 'global',
                workspaceName: '全局工作区专用窗口',
                tabCount: window.tabs?.length || 0,
                isVisible: window.state !== 'minimized'
              }
            };
          }
        } catch {
          // 窗口不存在，清理引用
          console.log('🗑️ 全局专用窗口已不存在，需要重新创建');
          await this.clearGlobalWindowId();
        }
      }

      // 设置创建标志，防止并发创建
      this.isCreatingWindow = true;

      try {
        // 创建全局专用窗口
        console.log('🔨 创建新的全局工作区专用窗口');
        const window = await chrome.windows.create({
          type: 'normal',
          state: 'normal',
          focused: false, // 不获取焦点
          width: 1200,
          height: 800,
          left: 100,
          top: 100,
          url: chrome.runtime.getURL('workspace-placeholder.html') + '?workspaceId=global&workspaceName=' + encodeURIComponent('全局工作区专用窗口')
        });

        if (!window.id) {
          throw new Error('Failed to create global workspace window');
        }

        // 保存全局窗口ID到存储和内存
        await this.saveGlobalWindowId(window.id);

        // 创建后立即最小化窗口
        try {
          await chrome.windows.update(window.id, { state: 'minimized' });
        } catch (error) {
          console.warn('最小化窗口失败，但窗口创建成功:', error);
        }
      } finally {
        // 清除创建标志
        this.isCreatingWindow = false;
      }

      console.log(`✅ 成功创建全局工作区专用窗口 -> 窗口ID ${this.globalWorkspaceWindowId}`);

      return {
        success: true,
        data: {
          id: this.globalWorkspaceWindowId!,
          workspaceId: 'global',
          workspaceName: '全局工作区专用窗口',
          tabCount: 1, // 新创建的窗口包含一个占位符标签页
          isVisible: false // 窗口默认最小化，所以不可见
        }
      };
    } catch (error) {
      console.error(`创建全局工作区专用窗口失败`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to create global workspace window',
          details: error,
        },
      };
    }
  }



  /**
   * 为工作区创建专用窗口（保持向后兼容）
   * 现在所有工作区都使用同一个全局专用窗口
   */
  static async createWorkspaceWindow(
    _workspaceId: string,
    _workspaceName: string
  ): Promise<OperationResult<WindowInfo>> {
    return await this.getOrCreateGlobalWorkspaceWindow();
  }

  /**
   * 获取工作区的专用窗口ID（现在所有工作区共享同一个窗口）
   */
  static getWorkspaceWindowId(_workspaceId: string): number | undefined {
    return this.globalWorkspaceWindowId || undefined;
  }

  /**
   * 获取窗口对应的工作区ID（现在返回全局标识）
   */
  static getWindowWorkspaceId(windowId: number): string | undefined {
    return windowId === this.globalWorkspaceWindowId ? 'global' : undefined;
  }

  /**
   * 获取全局专用窗口ID
   */
  static getGlobalWorkspaceWindowId(): number | undefined {
    return this.globalWorkspaceWindowId || undefined;
  }

  /**
   * 将标签页移动到全局专用窗口
   */
  static async moveTabsToWorkspaceWindow(
    tabIds: number[],
    workspaceId: string,
    workspaceName: string
  ): Promise<OperationResult<void>> {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }

      console.log(`🔄 开始移动 ${tabIds.length} 个标签页到全局专用窗口（来自工作区: ${workspaceName}）`, {
        tabIds,
        workspaceId,
        timestamp: new Date().toISOString()
      });

      // 确保全局专用窗口存在
      const windowResult = await this.getOrCreateGlobalWorkspaceWindow();
      if (!windowResult.success) {
        return { success: false, error: windowResult.error };
      }

      const windowId = windowResult.data!.id;

      // 窗口保护机制已移除，直接移动标签页

      // Workona 风格：直接移动标签页，不再处理固定状态
      console.log(`🔍 开始验证 ${tabIds.length} 个标签页的有效性...`);
      const validTabIds = [];

      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          validTabIds.push(tabId);
          console.log(`📋 验证标签页 ${tabId}: "${tab.title}" (${tab.url}) - 有效`);
        } catch (error) {
          console.warn(`⚠️ 标签页 ${tabId} 不存在或无法访问，跳过移动:`, error);
          // 继续处理其他标签页，不中断整个流程
        }
      }

      if (validTabIds.length === 0) {
        console.log(`⚠️ 没有有效的标签页需要移动`);
        return { success: true };
      }

      // Workona 风格：不再处理固定状态

      // 移动有效的标签页到专用窗口
      try {
        // 安全性检查：确保validTabIds不为空且包含有效的标签页ID
        if (validTabIds.length === 0) {
          console.log(`⚠️ 验证后发现没有有效的标签页需要移动`);
          return { success: true };
        }

        // 过滤掉无效的标签页ID（undefined, null, 0等）
        const safeTabIds = validTabIds.filter(id => id && typeof id === 'number' && id > 0);

        if (safeTabIds.length === 0) {
          console.log(`⚠️ 过滤后没有安全的标签页ID可以移动`);
          return { success: true };
        }

        if (safeTabIds.length !== validTabIds.length) {
          console.warn(`⚠️ 发现 ${validTabIds.length - safeTabIds.length} 个无效的标签页ID，已过滤`);
        }

        await chrome.tabs.move(safeTabIds, {
          windowId: windowId,
          index: -1 // 移动到窗口末尾
        });

        console.log(`✅ 成功移动 ${safeTabIds.length} 个标签页到专用窗口 ${windowId}`);
      } catch (moveError) {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveError);
        throw moveError;
      }

      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 将特定工作区的标签页从全局专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(
    workspaceId: string,
    targetWindowId?: number
  ): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`从全局专用窗口移动工作区 ${workspaceId} 的标签页到主窗口`);

      // 使用全局专用窗口ID
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在`);
        return { success: true, data: [] };
      }

      // 获取目标工作区信息
      // 使用静态导入的 StorageManager
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        console.log(`获取工作区 ${workspaceId} 信息失败:`, workspaceResult.error);
        return { success: true, data: [] };
      }

      const workspace = workspaceResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      // 获取专用窗口中的所有标签页
      const tabs = await chrome.tabs.query({ windowId });

      // 基于 Workona ID 映射过滤出属于目标工作区的标签页（包括会话临时标签页）
      const workspaceTabs = [];
      // 使用静态导入的 WorkonaTabManager

      console.log(`🔍 检查专用窗口中的 ${tabs.length} 个标签页，识别属于工作区 "${workspace.name}" 的标签页...`);

      for (const tab of tabs) {
        if (tab.url?.includes('workspace-placeholder.html')) {
          console.log(`🚫 跳过占位符页面: ${tab.url}`);
          continue; // 排除占位符页面
        }

        if (!tab.id) {
          console.log(`⚠️ 标签页无ID，跳过: ${tab.url}`);
          continue;
        }

        // 检查是否有 Workona ID 映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          // 检查 Workona ID 是否属于目标工作区
          const workonaId = workonaIdResult.data;
          const workspaceIdFromMapping = workonaId.split('-')[1]; // 从 t-{workspaceId}-{uuid} 提取工作区ID

          if (workspaceIdFromMapping === workspaceId) {
            workspaceTabs.push(tab);
            console.log(`✅ 找到工作区标签页: ${tab.title} (${tab.url}) - Workona ID: ${workonaId}`);
          } else {
            console.log(`❌ 标签页属于其他工作区: ${tab.title} - 工作区ID: ${workspaceIdFromMapping}`);
          }
        } else {
          // 回退到传统的 URL 匹配（兼容性）
          const isWorkspaceTab = workspaceUrls.some(url => tab.url?.startsWith(url));
          if (isWorkspaceTab) {
            workspaceTabs.push(tab);
            console.log(`✅ 通过URL匹配找到工作区标签页: ${tab.title} (${tab.url})`);
          } else {
            console.log(`❌ 非工作区标签页: ${tab.title} (${tab.url})`);
          }
        }
      }

      console.log(`📊 在全局专用窗口中找到工作区 "${workspace.name}" 的 ${workspaceTabs.length} 个标签页`);

      if (workspaceTabs.length === 0) {
        console.log(`全局专用窗口中没有工作区 "${workspace.name}" 的标签页需要移动`);
        return { success: true, data: [] };
      }

      // 确定目标窗口
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        // 获取当前活跃窗口作为目标
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id!;
      }

      console.log(`从全局专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);

      // Workona 风格：直接移动标签页，不处理固定状态
      const tabIds = workspaceTabs.map(tab => tab.id!);

      console.log(`🚀 移动 ${tabIds.length} 个标签页到主窗口 ${targetWindow}`);

      // 移动标签页
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });

      console.log(`✅ 成功移动 ${tabIds.length} 个标签页到主窗口`);
      // 转换为 TabInfo 格式
      const tabInfos: TabInfo[] = workspaceTabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow!,
        index: tab.index
      }));

      console.log(`✅ 成功移动 ${workspaceTabs.length} 个标签页到主窗口（包括会话临时标签页）`);

      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从全局专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from global workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭全局专用窗口
   */
  static async closeWorkspaceWindow(_workspaceId: string): Promise<OperationResult<void>> {
    return await this.closeGlobalWorkspaceWindow();
  }

  /**
   * 关闭全局专用窗口
   */
  static async closeGlobalWorkspaceWindow(): Promise<OperationResult<void>> {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在，无需关闭`);
        return { success: true };
      }

      console.log(`关闭全局专用窗口: ${windowId}`);

      // 先移动所有标签页到主窗口
      await this.moveTabsFromWorkspaceWindow('global');

      // 关闭专用窗口
      await chrome.windows.remove(windowId);

      // 清理引用
      this.globalWorkspaceWindowId = null;

      console.log(`成功关闭全局专用窗口: ${windowId}`);

      return { success: true };
    } catch (error) {
      console.error(`关闭全局专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to close global workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 获取所有工作区专用窗口信息（现在只有一个全局窗口）
   */
  static async getAllWorkspaceWindows(): Promise<OperationResult<WindowInfo[]>> {
    try {
      const windowInfos: WindowInfo[] = [];

      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId, { populate: true });

          windowInfos.push({
            id: this.globalWorkspaceWindowId,
            workspaceId: 'global',
            workspaceName: '全局工作区专用窗口',
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== 'minimized'
          });
        } catch {
          // 窗口不存在，清理引用
          this.globalWorkspaceWindowId = null;
        }
      }

      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to get workspace windows',
          details: error,
        },
      };
    }
  }

  /**
   * 更新窗口标题（现在更新全局窗口标题）
   */
  static async updateWindowTitle(
    _workspaceId: string,
    _workspaceName: string,
    tabCount: number
  ): Promise<OperationResult<void>> {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        return { success: true }; // 窗口不存在，忽略
      }

      // 通过更新占位符页面的标题来间接更新窗口标题
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(tab =>
        tab.url?.includes('workspace-placeholder.html')
      );

      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL('workspace-placeholder.html') +
          `?workspaceId=global&workspaceName=${encodeURIComponent('全局工作区专用窗口')}&tabCount=${tabCount}`;

        await chrome.tabs.update(placeholderTab.id!, { url: newUrl });
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to update window title',
          details: error,
        },
      };
    }
  }
}
