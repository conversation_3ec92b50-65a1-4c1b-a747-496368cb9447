// Service Worker 环境 polyfill
if (typeof window === 'undefined') {
  // 创建一个简单的 window 对象 polyfill
  (globalThis as any).window = {
    dispatchEvent: (event: Event) => {
      // 在 Service Worker 中，我们可以简单地忽略这些事件
      console.log('Service Worker: 忽略 window.dispatchEvent 调用:', event.type);
      return true;
    }
  };
}

import { StorageManager } from '../utils/storage';
import { WorkspaceSwitcher } from '../utils/workspaceSwitcher';
import { MigrationManager } from '../utils/dataMigration';
import { TabManager, UserTabsRealTimeMonitor } from '../utils/tabs';
import { WorkonaTabManager } from '../utils/workonaTabManager';
import { WorkspaceManager } from '../utils/workspace';
import { WorkspaceSessionManager } from '../utils/workspaceSessionManager';
import { COMMANDS, STORAGE_KEYS } from '../utils/constants';
import { WorkonaTabManager } from '../utils/workonaTabManager';

/**
 * Chrome扩展后台脚本
 */
class BackgroundService {
  // 🛡️ Service Worker生命周期管理
  private static isFirstRealStartup = true;
  private static lastInitTimestamp = 0;
  private static readonly SERVICE_WORKER_WAKEUP_THRESHOLD = 5 * 60 * 1000; // 5分钟

  constructor() {
    this.init();
  }

  /**
   * 初始化后台服务
   */
  private async init(): Promise<void> {
    const initStartTime = Date.now();
    console.log('🚀 BackgroundService 初始化开始');

    // 设置侧边栏行为
    await this.setupSidePanel();

    // 监听命令
    this.setupCommandListeners();

    // 监听标签页事件
    this.setupTabListeners();

    // 监听存储变化
    this.setupStorageListeners();

    // 检查并执行数据迁移（新增）
    await this.checkAndMigrateData();

    // 🛡️ 智能工作区状态管理（替换原来的清除逻辑）
    await this.smartWorkspaceStateManagement();

    // 恢复浏览器重启后的标签页映射关系
    await this.restoreTabMappingsAfterRestart();

    // 初始化默认数据
    await this.initializeDefaultData();

    // 启动用户标签页实时监控
    await this.startUserTabsRealTimeMonitoring();

    const initDuration = Date.now() - initStartTime;
    console.log(`✅ BackgroundService 初始化完成 (耗时: ${initDuration}ms)`);

    // 记录Service Worker生命周期信息
    this.logServiceWorkerLifecycle();

    // 设置Service Worker生命周期监控
    this.setupServiceWorkerLifecycleMonitoring();
  }

  /**
   * 📊 记录Service Worker生命周期信息
   */
  private logServiceWorkerLifecycle(): void {
    const now = Date.now();
    const timeSinceLastInit = now - BackgroundService.lastInitTimestamp;

    console.log('📊 Service Worker生命周期信息:', {
      isFirstRealStartup: BackgroundService.isFirstRealStartup,
      lastInitTimestamp: new Date(BackgroundService.lastInitTimestamp).toISOString(),
      timeSinceLastInit: `${Math.round(timeSinceLastInit / 1000)}秒`,
      currentTimestamp: new Date(now).toISOString(),
      wakeupThreshold: `${BackgroundService.SERVICE_WORKER_WAKEUP_THRESHOLD / 1000}秒`
    });
  }

  /**
   * 🔄 设置Service Worker生命周期监控
   */
  private setupServiceWorkerLifecycleMonitoring(): void {
    // 监听扩展启动事件
    if (chrome.runtime.onStartup) {
      chrome.runtime.onStartup.addListener(async () => {
        console.log('🚀 Chrome扩展启动事件触发 - 浏览器重启');
        BackgroundService.isFirstRealStartup = true;
        BackgroundService.lastInitTimestamp = 0;

        // 🔄 浏览器重启后重置工作区选择状态
        await this.resetWorkspaceStateOnBrowserRestart();
      });
    }

    // 监听扩展安装事件
    if (chrome.runtime.onInstalled) {
      chrome.runtime.onInstalled.addListener(async (details) => {
        console.log('📦 Chrome扩展安装/更新事件:', details.reason);
        BackgroundService.isFirstRealStartup = true;
        BackgroundService.lastInitTimestamp = 0;

        if (details.reason === 'install') {
          console.log('🎉 扩展首次安装');
          // 首次安装时也重置状态
          await this.resetWorkspaceStateOnBrowserRestart();
        } else if (details.reason === 'update') {
          console.log('🔄 扩展更新');
        }
      });
    }

    // 监听扩展挂起事件（如果可用）
    if (chrome.runtime.onSuspend) {
      chrome.runtime.onSuspend.addListener(() => {
        console.log('😴 Service Worker即将挂起');
      });
    }

    // 监听扩展挂起取消事件（如果可用）
    if (chrome.runtime.onSuspendCanceled) {
      chrome.runtime.onSuspendCanceled.addListener(() => {
        console.log('🔄 Service Worker挂起被取消');
      });
    }
  }

  /**
   * 启动用户标签页实时监控
   */
  private async startUserTabsRealTimeMonitoring(): Promise<void> {
    try {
      UserTabsRealTimeMonitor.startMonitoring();
      console.log('📊 用户标签页实时监控已启动');
    } catch (error) {
      console.warn('启动用户标签页实时监控失败:', error);
    }
  }

  /**
   * 刷新用户标签页实时监控
   */
  private async refreshUserTabsMonitoring(): Promise<void> {
    try {
      // 🛡️ 工作区持久性保护：优先使用当前工作区，避免意外检测导致状态变化

      // 首先尝试获取当前存储的工作区
      let activeWorkspace = null;
      const currentWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
        activeWorkspace = currentWorkspaceResult.data;
        console.log(`🎯 使用当前存储的工作区进行监控刷新: ${activeWorkspace.name}`);
      } else {
        // 只有在没有当前工作区时才进行检测
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          activeWorkspace = activeWorkspaceResult.data;
          console.log(`🔍 通过检测获取工作区进行监控刷新: ${activeWorkspace.name}`);
        }
      }

      if (activeWorkspace) {
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(activeWorkspace.id);
      } else {
        console.log('⚠️ 没有活跃工作区，跳过用户标签页监控刷新');
      }
    } catch (error) {
      console.warn('刷新用户标签页实时监控失败:', error);
    }
  }

  /**
   * 同步标签页编辑后的状态
   */
  private async syncTabAfterEdit(tabId: number, changeInfo: chrome.tabs.TabChangeInfo, _tab: chrome.tabs.Tab): Promise<void> {
    try {
      // 检查是否有URL或标题变化
      if (changeInfo.url || changeInfo.title) {
        // 使用静态导入的 WorkonaTabManager
        await WorkonaTabManager.syncTabAfterEdit(tabId, changeInfo.url, changeInfo.title);
      }
    } catch (error) {
      console.warn('同步标签页编辑状态失败:', error);
    }
  }

  /**
   * 处理标签页固定状态变化
   */
  private async handleTabPinnedStateChange(tabId: number, isPinned: boolean, tab: chrome.tabs.Tab): Promise<void> {
    try {
      console.log(`📌 标签页固定状态变化: ${tabId} -> ${isPinned ? '固定' : '取消固定'}`);

      // 获取标签页的 Workona ID
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        console.log(`⚠️ 标签页 ${tabId} 没有 Workona ID 映射，跳过固定状态同步`);
        return;
      }

      const workonaId = workonaIdResult.data;
      const workspaceId = workonaId.split('-')[1]; // 从 t-{workspaceId}-{uuid} 提取工作区ID

      // 更新 TabIdMapping 中的固定状态元数据
      await WorkonaTabManager.updateTabMetadata(workonaId, {
        metadata: {
          isPinned: isPinned,
          pinnedAt: isPinned ? Date.now() : undefined,
          unpinnedAt: !isPinned ? Date.now() : undefined
        }
      });

      // 更新工作区的固定状态存储
      if (isPinned) {
        await this.updateWorkspacePinnedTabIds(workspaceId, tabId);
      } else {
        await this.removeFromWorkspacePinnedTabIds(workspaceId, tabId);
      }

      // 更新标签页的会话存储
      try {
        await chrome.scripting.executeScript({
          target: { tabId: tabId },
          func: (isPinned: boolean) => {
            const existingData = sessionStorage.getItem('workonaData');
            if (existingData) {
              const workonaData = JSON.parse(existingData);
              workonaData.isPinned = isPinned;
              workonaData.timestamp = Date.now();
              sessionStorage.setItem('workonaData', JSON.stringify(workonaData));
              console.log(`📝 更新标签页会话存储的固定状态: ${isPinned}`);
            }
          },
          args: [isPinned]
        });
      } catch (error) {
        console.warn(`⚠️ 更新标签页 ${tabId} 会话存储失败:`, error);
      }

      console.log(`✅ 已同步标签页 ${tabId} 的固定状态: ${isPinned ? '固定' : '取消固定'}`);
    } catch (error) {
      console.warn('处理标签页固定状态变化失败:', error);
    }
  }

  /**
   * 设置侧边栏
   */
  private async setupSidePanel(): Promise<void> {
    try {
      // 设置侧边栏在点击扩展图标时打开
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error('Failed to setup side panel:', error);
    }
  }

  /**
   * 设置命令监听器
   */
  private setupCommandListeners(): void {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log('Command received:', command);
      
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          case 'test_auto_classify':
            // 测试自动分类功能
            await this.testAutoClassify();
            break;
          default:
            console.log('Unknown command:', command);
        }
      } catch (error) {
        console.error('Error handling command:', command, error);
      }
    });
  }

  /**
   * 测试自动分类功能
   */
  private async testAutoClassify(): Promise<void> {
    try {
      console.log('🧪 [测试] 开始测试自动分类功能...');

      // 获取当前活跃标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        console.log('❌ [测试] 没有找到活跃标签页');
        return;
      }

      const activeTab = tabs[0];
      console.log('🧪 [测试] 当前活跃标签页:', {
        id: activeTab.id,
        url: activeTab.url,
        title: activeTab.title
      });

      if (!activeTab.id || !activeTab.url) {
        console.log('❌ [测试] 活跃标签页缺少ID或URL');
        return;
      }

      // 手动触发自动分类
      console.log('🧪 [测试] 手动触发自动分类...');
      const result = await TabManager.autoClassifyNewTab(activeTab.id, activeTab.url);
      console.log('🧪 [测试] 自动分类结果:', result);

      // 验证结果
      const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      console.log('🧪 [测试] 验证 Workona ID:', verifyResult);

    } catch (error) {
      console.error('❌ [测试] 测试自动分类功能失败:', error);
    }
  }

  /**
   * 设置标签页监听器
   */
  private setupTabListeners(): void {
    // 监听标签页激活（记录活跃标签页状态）
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        // 不再自动切换工作区，但记录标签页状态变化
        console.log('标签页激活，记录状态变化');

        // 获取标签页的Workona ID
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeInfo.tabId);

        if (workonaIdResult.success && workonaIdResult.data) {
          // 设置为当前活跃标签页
          await WorkspaceSessionManager.setActiveTab(workonaIdResult.data);
        }

        // 同步当前工作区状态
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      } catch (error) {
        console.error('Error handling tab activation:', error);
      }
    });



    // 监听标签页移动（记录标签页顺序变化）
    chrome.tabs.onMoved.addListener(async (_tabId, _moveInfo) => {
      try {
        console.log('标签页位置变化，同步工作区状态');

        // 延迟一点时间确保移动完成
        setTimeout(async () => {
          await WorkspaceSessionManager.syncCurrentWorkspaceState();
        }, 100);
      } catch (error) {
        console.error('Error handling tab move:', error);
      }
    });

    // 监听标签页创建（概念性重构：自动分类）
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log('🆕 [LISTENER] 标签页创建事件触发:', {
          id: tab.id,
          url: tab.url || '(no URL yet)',
          title: tab.title || '(no title yet)',
          status: tab.status,
          timestamp: new Date().toISOString()
        });

        // 注意：新创建的标签页可能还没有URL，主要依赖 onUpdated 监听器进行分类
        // 这里只处理有明确URL的情况
        if (tab.id && tab.url &&
            !tab.url.includes('chrome://') &&
            !tab.url.includes('chrome-extension://') &&
            !tab.url.includes('about:') &&
            tab.url !== 'chrome://newtab/') {
          console.log(`🎯 [LISTENER] 标签页创建时尝试自动分类: ID=${tab.id}, URL=${tab.url}`);
          try {
            console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
            const result = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
            console.log(`✨ [LISTENER] 已自动分类新标签页: ${tab.url}`);
          } catch (error) {
            console.error('❌ [LISTENER] 自动分类新标签页失败:', error);
          }
        } else {
          console.log(`⏭️ 跳过标签页创建时的自动分类: ID=${tab.id}, URL=${tab.url}, 原因: ${
            !tab.id ? '无ID' :
            !tab.url ? '无URL' :
            tab.url.includes('chrome://') ? '系统页面' :
            tab.url.includes('chrome-extension://') ? '扩展页面' :
            tab.url.includes('about:') ? 'about页面' :
            tab.url === 'chrome://newtab/' ? '新标签页' :
            '未知原因'
          }`);
        }

        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_created');

        // 触发立即状态检查，提升实时性
        // 使用静态导入的 UserTabsRealTimeMonitor
        await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
      } catch (error) {
        console.error('Error handling tab creation:', error);
      }
    });

    // 监听标签页更新（增强版：包含自动分类）
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      try {
        // 处理URL变化或加载完成的情况
        if ((changeInfo.url || changeInfo.status === 'complete') && tab.url) {
          console.log('🔄 [LISTENER] 标签页更新事件触发:', {
            id: tabId,
            url: tab.url,
            title: tab.title,
            status: tab.status,
            changeInfo: changeInfo,
            timestamp: new Date().toISOString()
          });

          // 概念性重构：自动分类更新的标签页
          // 检查是否需要分类（URL变化或首次加载完成）
          const shouldClassify = tab.url &&
              !tab.url.includes('chrome://') &&
              !tab.url.includes('chrome-extension://') &&
              !tab.url.includes('about:') &&
              tab.url !== 'chrome://newtab/' &&
              tab.url !== '';

          if (shouldClassify) {
            console.log(`🎯 [LISTENER] 标签页更新时尝试自动分类: ID=${tabId}, URL=${tab.url}`);
            try {
              console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
              const result = await TabManager.autoClassifyNewTab(tabId, tab.url);
              console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
              console.log(`✨ [LISTENER] 已自动分类更新的标签页: ${tab.url}`);
            } catch (error) {
              console.error('❌ [LISTENER] 自动分类更新标签页失败:', error);
            }
          } else {
            console.log(`⏭️ 跳过标签页更新时的自动分类: ID=${tabId}, URL=${tab.url}, 原因: ${
              !tab.url ? '无URL' :
              tab.url.includes('chrome://') ? '系统页面' :
              tab.url.includes('chrome-extension://') ? '扩展页面' :
              tab.url.includes('about:') ? 'about页面' :
              tab.url === 'chrome://newtab/' ? '新标签页' :
              tab.url === '' ? '空URL' :
              '未知原因'
            }`);
          }

          // 同步标签页编辑后的状态
          await this.syncTabAfterEdit(tabId, changeInfo, tab);

          // 通知前端更新状态
          await this.notifyGlobalUserTabsStateChange('tab_updated');

          // 触发立即状态检查，提升实时性
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
        } else {
          console.log(`⏭️ 跳过标签页更新事件: ID=${tabId}, 原因: ${
            !changeInfo.url && changeInfo.status !== 'complete' ? '非URL变化且非完成状态' :
            !tab.url ? '无URL' :
            '未知原因'
          }`);
        }

        // 处理固定状态变化（独立于URL/状态变化）
        if (changeInfo.pinned !== undefined) {
          await this.handleTabPinnedStateChange(tabId, changeInfo.pinned, tab);
        }
      } catch (error) {
        console.error('Error handling tab update:', error);
      }
    });

    // 重复的监听器已移除，使用上面的增强版监听器

    // 监听标签页移除（增强版：清理 Workona ID 映射）
    chrome.tabs.onRemoved.addListener(async (tabId, _removeInfo) => {
      try {
        console.log('Tab removed:', tabId);

        // Workona 风格：清理标签页的 Workona ID 映射
        try {

          // 获取标签页的 Workona ID
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
          if (workonaIdResult.success && workonaIdResult.data) {
            const workonaId = workonaIdResult.data;
            const workspaceId = workonaId.split('-')[1]; // 从 t-{workspaceId}-{uuid} 提取工作区ID

            // 从工作区中移除 Workona 标签页ID
            await WorkspaceManager.removeWorkonaTabId(workspaceId, workonaId);

            // 移除 Workona ID 映射
            await WorkonaTabManager.removeTabMapping(workonaId);

            console.log(`🗑️ 清理已删除标签页的 Workona ID: ${workonaId}`);
          }
        } catch (workonaError) {
          console.error('清理 Workona ID 映射失败:', workonaError);
        }

        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_removed');

        // 触发立即状态检查，提升实时性
        const { UserTabsRealTimeMonitor } = await import('../utils/tabs');
        await UserTabsRealTimeMonitor.triggerImmediateStateCheck();

        // 触发用户标签页实时监控刷新
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error('Error handling tab removal:', error);
      }
    });

    // 监听标签页移动
    chrome.tabs.onMoved.addListener(async (tabId, moveInfo) => {
      try {
        console.log('Tab moved:', tabId, moveInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_moved');

        // 触发用户标签页实时监控刷新
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error('Error handling tab move:', error);
      }
    });

    // 监听标签页附加到窗口
    chrome.tabs.onAttached.addListener(async (tabId, attachInfo) => {
      try {
        console.log('Tab attached:', tabId, attachInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_attached');

        // 触发用户标签页实时监控刷新
        await this.refreshUserTabsMonitoring();
      } catch (error) {
        console.error('Error handling tab attach:', error);
      }
    });

    // 监听标签页从窗口分离
    chrome.tabs.onDetached.addListener(async (tabId, detachInfo) => {
      try {
        console.log('Tab detached:', tabId, detachInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_detached');
      } catch (error) {
        console.error('Error handling tab detach:', error);
      }
    });
  }



  /**
   * 设置存储监听器
   */
  private setupStorageListeners(): void {
    StorageManager.onChanged((changes) => {
      console.log('Storage changed:', changes);
      
      // 通知侧边栏更新
      this.notifySidePanelUpdate(changes);
    });
  }

  /**
   * 恢复浏览器重启后的标签页映射关系（纯 Workona ID 血缘跟踪）
   */
  private async restoreTabMappingsAfterRestart(): Promise<void> {
    try {
      console.log('🔄 开始恢复浏览器重启后的标签页映射关系（纯 Workona ID 血缘跟踪）...');

      // 获取所有当前打开的标签页
      const allTabs = await chrome.tabs.query({});
      console.log(`📊 发现 ${allTabs.length} 个当前标签页`);

      // 清理所有现有的映射（因为 Chrome 标签页 ID 已经改变）
      await StorageManager.saveTabIdMappings([]);
      console.log('🗑️ 已清理旧的标签页映射');

      let restoredCount = 0;
      let userTabsCount = 0;

      // 遍历所有标签页，通过会话存储中的 Workona 数据恢复映射关系
      for (const tab of allTabs) {
        if (!tab.id || !tab.url) continue;

        // 跳过系统页面
        if (tab.url.includes('chrome://') ||
            tab.url.includes('chrome-extension://') ||
            tab.url.includes('about:')) {
          continue;
        }

        try {
          // 尝试从标签页的会话存储中获取 Workona 数据
          const results = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: () => {
              const workonaDataStr = sessionStorage.getItem('workonaData');
              if (workonaDataStr) {
                try {
                  return JSON.parse(workonaDataStr);
                } catch {
                  return null;
                }
              }
              return null;
            }
          });

          const workonaData = results[0]?.result;

          if (workonaData && workonaData.workonaId) {
            // 找到了 Workona 数据，重新建立映射关系
            console.log(`🔗 通过会话存储恢复标签页映射: ${workonaData.workonaId} <-> ${tab.id}`);

            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              workonaData.workonaId,
              tab.id,
              workonaData.workspaceId,
              workonaData.websiteId || undefined,
              {
                isWorkspaceCore: workonaData.isWorkspaceCore,
                source: 'session_restored'
              }
            );

            // 如果映射创建成功，重置固定状态（浏览器重启后不保持固定状态）
            if (mappingResult.success) {
              await WorkonaTabManager.updateTabMetadata(workonaData.workonaId, {
                metadata: {
                  isPinned: false, // 浏览器重启后重置固定状态
                  unpinnedAt: Date.now()
                }
              });

              console.log(`📌 重置固定状态: ${workonaData.workonaId} -> 浏览器重启后重置为非固定`);
            }

            if (mappingResult.success) {
              restoredCount++;
              console.log(`✅ 成功恢复标签页映射: ${workonaData.workonaId} <-> ${tab.id} (核心: ${workonaData.isWorkspaceCore})`);

              // 如果标签页原本是固定状态，更新工作区的固定状态存储
              if (tab.pinned) {
                await this.updateWorkspacePinnedTabIds(workonaData.workspaceId, tab.id);
              }
            }
          } else {
            // 没有 Workona 数据，这是一个新的用户标签页
            try {
              const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url);
              if (classifyResult.success) {
                userTabsCount++;
                console.log(`🆕 为新用户标签页创建映射: ${tab.title}`);
              }
            } catch (error) {
              console.warn(`⚠️ 为用户标签页创建映射失败: ${tab.title}`, error);
            }
          }
        } catch (error) {
          // 无法访问标签页的会话存储（可能是跨域限制），作为用户标签页处理
          console.warn(`⚠️ 无法访问标签页 ${tab.id} 的会话存储:`, error);
          try {
            const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            if (classifyResult.success) {
              userTabsCount++;
              console.log(`🆕 为无法访问会话存储的标签页创建用户映射: ${tab.title}`);
            }
          } catch (classifyError) {
            console.warn(`⚠️ 为用户标签页创建映射失败: ${tab.title}`, classifyError);
          }
        }
      }

      console.log(`✅ 标签页映射恢复完成: 恢复 ${restoredCount} 个 Workona 标签页, 创建 ${userTabsCount} 个用户标签页映射`);

      // 恢复完成后，清理所有工作区的固定状态存储
      await this.cleanupAllWorkspacePinnedStates();
    } catch (error) {
      console.error('❌ 恢复标签页映射失败:', error);
    }
  }

  /**
   * 更新工作区的固定标签页ID列表
   * 在浏览器重启后，Chrome ID 会变化，需要更新存储的固定状态
   */
  private async updateWorkspacePinnedTabIds(workspaceId: string, newChromeId: number): Promise<void> {
    try {
      const storageKey = `workspacePinnedTabIds_${workspaceId}`;
      const result = await chrome.storage.local.get([storageKey]);
      let pinnedTabIds: number[] = result[storageKey] || [];

      // 验证现有的 Chrome ID 是否仍然有效，清理无效的 ID
      const validPinnedTabIds: number[] = [];
      for (const tabId of pinnedTabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab && tab.pinned) {
            validPinnedTabIds.push(tabId);
          }
        } catch (error) {
          // 标签页不存在或无法访问，跳过
          console.log(`🗑️ 清理无效的固定标签页 ID: ${tabId}`);
        }
      }

      // 添加新的 Chrome ID（如果不在列表中）
      if (!validPinnedTabIds.includes(newChromeId)) {
        validPinnedTabIds.push(newChromeId);
        console.log(`📌 更新工作区 ${workspaceId} 的固定状态存储，添加新 Chrome ID: ${newChromeId}`);
      }

      // 只有在列表发生变化时才更新存储
      if (validPinnedTabIds.length !== pinnedTabIds.length ||
          !validPinnedTabIds.every(id => pinnedTabIds.includes(id))) {
        await chrome.storage.local.set({ [storageKey]: validPinnedTabIds });
        console.log(`💾 工作区 ${workspaceId} 固定状态存储已更新: ${validPinnedTabIds.length} 个有效标签页`);
      }
    } catch (error) {
      console.warn(`⚠️ 更新工作区固定状态存储失败:`, error);
    }
  }

  /**
   * 从工作区的固定标签页ID列表中移除指定的 Chrome ID
   */
  private async removeFromWorkspacePinnedTabIds(workspaceId: string, chromeId: number): Promise<void> {
    try {
      const storageKey = `workspacePinnedTabIds_${workspaceId}`;
      const result = await chrome.storage.local.get([storageKey]);
      let pinnedTabIds: number[] = result[storageKey] || [];

      // 移除指定的 Chrome ID
      const originalLength = pinnedTabIds.length;
      pinnedTabIds = pinnedTabIds.filter(id => id !== chromeId);

      if (pinnedTabIds.length !== originalLength) {
        if (pinnedTabIds.length === 0) {
          // 如果没有固定标签页了，删除存储键
          await chrome.storage.local.remove([storageKey]);
          console.log(`🗑️ 删除工作区 ${workspaceId} 的空固定状态存储`);
        } else {
          await chrome.storage.local.set({ [storageKey]: pinnedTabIds });
          console.log(`📌 从工作区 ${workspaceId} 的固定状态存储中移除 Chrome ID: ${chromeId}`);
        }
      }
    } catch (error) {
      console.warn(`⚠️ 从工作区固定状态存储中移除 Chrome ID 失败:`, error);
    }
  }

  /**
   * 清理所有工作区的固定状态存储
   * 移除无效的 Chrome ID，确保固定状态存储的准确性
   */
  private async cleanupAllWorkspacePinnedStates(): Promise<void> {
    try {
      console.log('🧹 开始清理所有工作区的固定状态存储...');

      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.warn('⚠️ 获取工作区列表失败，跳过固定状态清理');
        return;
      }

      const workspaces = workspacesResult.data!;
      let totalCleaned = 0;

      for (const workspace of workspaces) {
        const storageKey = `workspacePinnedTabIds_${workspace.id}`;
        const result = await chrome.storage.local.get([storageKey]);
        const pinnedTabIds: number[] = result[storageKey] || [];

        if (pinnedTabIds.length === 0) continue;

        // 验证每个 Chrome ID 是否仍然有效且为固定状态
        const validPinnedTabIds: number[] = [];
        for (const tabId of pinnedTabIds) {
          try {
            const tab = await chrome.tabs.get(tabId);
            if (tab && tab.pinned) {
              validPinnedTabIds.push(tabId);
            } else {
              totalCleaned++;
              console.log(`🗑️ 清理工作区 "${workspace.name}" 的无效固定标签页 ID: ${tabId}`);
            }
          } catch (error) {
            totalCleaned++;
            console.log(`🗑️ 清理工作区 "${workspace.name}" 的不存在标签页 ID: ${tabId}`);
          }
        }

        // 更新存储（如果有变化）
        if (validPinnedTabIds.length !== pinnedTabIds.length) {
          if (validPinnedTabIds.length === 0) {
            // 如果没有有效的固定标签页，删除存储键
            await chrome.storage.local.remove([storageKey]);
            console.log(`🗑️ 删除工作区 "${workspace.name}" 的空固定状态存储`);
          } else {
            await chrome.storage.local.set({ [storageKey]: validPinnedTabIds });
            console.log(`💾 更新工作区 "${workspace.name}" 的固定状态存储: ${validPinnedTabIds.length} 个有效标签页`);
          }
        }
      }

      console.log(`✅ 固定状态清理完成，共清理 ${totalCleaned} 个无效条目`);
    } catch (error) {
      console.warn('⚠️ 清理工作区固定状态存储失败:', error);
    }
  }

  /**
   * 🛡️ 智能工作区状态管理
   * 区分真正的启动和Service Worker唤醒
   * 浏览器重启时重置工作区选择状态（用户需求）
   */
  private async smartWorkspaceStateManagement(): Promise<void> {
    try {
      const now = Date.now();
      const timeSinceLastInit = now - BackgroundService.lastInitTimestamp;

      console.log(`🔍 工作区状态管理检查: 距离上次初始化 ${Math.round(timeSinceLastInit / 1000)}秒`);

      // 如果距离上次初始化时间很短，可能是Service Worker唤醒
      if (timeSinceLastInit < BackgroundService.SERVICE_WORKER_WAKEUP_THRESHOLD && !BackgroundService.isFirstRealStartup) {
        console.log('🔄 检测到Service Worker唤醒，保持工作区状态');
        BackgroundService.lastInitTimestamp = now;
        return;
      }

      // 🔄 真正的启动事件（浏览器重启或扩展首次加载）- 重置工作区状态
      console.log('🔄 检测到真正的启动事件 - 浏览器重启或扩展首次加载');
      console.log('🔄 根据用户需求，重置工作区选择状态');

      // 执行工作区状态重置
      await this.resetWorkspaceStateOnBrowserRestart();

      BackgroundService.isFirstRealStartup = false;
      BackgroundService.lastInitTimestamp = now;
    } catch (error) {
      console.error('❌ 智能工作区状态管理失败:', error);
      // 发生错误时，仍然重置状态以确保用户需求得到满足
      try {
        await this.resetWorkspaceStateOnBrowserRestart();
      } catch (resetError) {
        console.error('❌ 重置工作区状态失败:', resetError);
      }
    }
  }

  /**
   * 🔄 尝试恢复工作区状态
   */
  private async attemptWorkspaceStateRecovery(): Promise<void> {
    try {
      console.log('🔄 尝试恢复工作区状态...');

      // 检查最近使用的工作区
      const lastActiveResult = await StorageManager.getLastActiveWorkspaceIds();
      if (lastActiveResult.success && lastActiveResult.data && lastActiveResult.data.length > 0) {
        const lastWorkspaceId = lastActiveResult.data[0];

        // 验证工作区是否存在
        const workspaceResult = await StorageManager.getWorkspace(lastWorkspaceId);
        if (workspaceResult.success) {
          console.log(`🔄 恢复最近使用的工作区: ${workspaceResult.data!.name}`);
          await StorageManager.setActiveWorkspaceId(lastWorkspaceId);

          // 更新工作区的活跃状态
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success && workspacesResult.data) {
            const workspaces = workspacesResult.data.map(workspace => ({
              ...workspace,
              isActive: workspace.id === lastWorkspaceId
            }));
            await StorageManager.saveWorkspaces(workspaces);
          }
          return;
        }
      }

      console.log('⚠️ 无法恢复工作区状态，保持未选择状态');
    } catch (error) {
      console.error('❌ 工作区状态恢复失败:', error);
    }
  }

  /**
   * 🔄 浏览器重启后重置工作区选择状态
   * 确保用户在浏览器重启后需要手动选择工作区
   */
  private async resetWorkspaceStateOnBrowserRestart(): Promise<void> {
    try {
      console.log('🔄 浏览器重启检测 - 开始重置工作区选择状态');

      // 1. 清除当前活跃工作区ID
      await StorageManager.setActiveWorkspaceId(null);
      console.log('✅ 已清除活跃工作区ID');

      // 2. 清除所有工作区的 isActive 状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data) {
        const workspaces = workspacesResult.data.map(workspace => ({
          ...workspace,
          isActive: false
        }));
        await StorageManager.saveWorkspaces(workspaces);
        console.log('✅ 已重置所有工作区的激活状态');
      }

      // 3. 清除工作区会话状态
      await this.clearWorkspaceSessionState();

      // 4. 通知侧边栏更新UI状态
      await this.notifyWorkspaceStateReset();

      console.log('✅ 浏览器重启后工作区状态重置完成');
    } catch (error) {
      console.error('❌ 重置工作区状态失败:', error);
    }
  }

  /**
   * 清除工作区会话状态
   */
  private async clearWorkspaceSessionState(): Promise<void> {
    try {
      // 清除当前会话
      const { WorkspaceSessionManager } = await import('../utils/workspaceSessionManager');
      if (WorkspaceSessionManager.clearCurrentSession) {
        await WorkspaceSessionManager.clearCurrentSession();
        console.log('✅ 已清除工作区会话状态');
      }
    } catch (error) {
      console.warn('⚠️ 清除工作区会话状态失败:', error);
    }
  }

  /**
   * 通知侧边栏工作区状态已重置
   */
  private async notifyWorkspaceStateReset(): Promise<void> {
    try {
      // 发送消息通知侧边栏更新
      chrome.runtime.sendMessage({
        type: 'WORKSPACE_STATE_RESET',
        timestamp: Date.now()
      }).catch(error => {
        console.log('发送工作区状态重置通知失败:', error);
      });

      // 触发存储变化事件以更新UI
      const changes = {
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: { newValue: null, oldValue: undefined }
      };
      this.notifySidePanelUpdate(changes);

      console.log('✅ 已通知侧边栏工作区状态重置');
    } catch (error) {
      console.warn('⚠️ 通知侧边栏状态重置失败:', error);
    }
  }

  /**
   * 清除活跃工作区状态（仅在必要时使用）
   * @deprecated 使用 resetWorkspaceStateOnBrowserRestart 替代
   */
  private async clearActiveWorkspaceOnStartup(): Promise<void> {
    try {
      console.log('🔄 清除活跃工作区状态，让用户手动选择');
      await StorageManager.setActiveWorkspaceId(null);

      // 同时清除所有工作区的 isActive 状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data) {
        const workspaces = workspacesResult.data.map(workspace => ({
          ...workspace,
          isActive: false
        }));
        await StorageManager.saveWorkspaces(workspaces);
      }

      console.log('✅ 活跃工作区状态已清除');
    } catch (error) {
      console.error('❌ 清除活跃工作区状态失败:', error);
    }
  }

  /**
   * 初始化默认数据
   */
  private async initializeDefaultData(): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data!.length === 0) {
        console.log('No workspaces found, creating default workspace templates');

        // 可以选择性地创建一些默认工作区
        // 这里暂时不自动创建，让用户自己选择
      }
    } catch (error) {
      console.error('Error initializing default data:', error);
    }
  }

  /**
   * 根据索引切换工作区
   */
  private async switchToWorkspaceByIndex(index: number): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('Failed to get workspaces:', workspacesResult.error);
        return;
      }

      const workspaces = workspacesResult.data!;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        const result = await WorkspaceSwitcher.switchToWorkspace(workspace.id);
        if (result.success) {
          console.log(`Switched to workspace: ${workspace.name}`);
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        } else {
          console.error('Failed to switch workspace:', result.error);
        }
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error('Error switching workspace by index:', error);
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  private async toggleSidePanel(): Promise<void> {
    try {
      // 获取当前活跃的标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id!;
        // 这里可以实现侧边栏的切换逻辑
        // 由于Chrome API限制，我们主要依赖用户点击扩展图标
        console.log('Toggle side panel for tab:', tabId);
      }
    } catch (error) {
      console.error('Error toggling side panel:', error);
    }
  }

  /**
   * 显示通知
   */
  private showNotification(message: string, icon?: string): void {
    try {
      // 创建简单的通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'WorkSpace Pro',
        message: `${icon || '🚀'} ${message}`,
      });
      console.log('Notification shown:', message);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  /**
   * 通知侧边栏更新
   */
  private notifySidePanelUpdate(_changes: { [key: string]: chrome.storage.StorageChange }): void {
    try {
      // 这里可以通过消息传递通知侧边栏更新
      // 由于侧边栏是独立的页面，我们主要依赖存储监听
      console.log('Notifying side panel of storage changes');
    } catch (error) {
      console.error('Error notifying side panel:', error);
    }
  }



  /**
   * 通知全局用户标签页状态变化
   */
  private async notifyGlobalUserTabsStateChange(eventType: string): Promise<void> {
    try {
      // 立即发送通知，然后再延迟发送一次确保操作完成
      const sendNotification = async () => {
        try {
          // 在background script中，只发送Chrome扩展消息，不使用window对象
          if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({
              type: 'USER_TABS_VISIBILITY_CHANGED',
              workspaceId: 'global',
              eventType: eventType
            }).catch(error => {
              console.log('发送用户标签页状态变化消息失败:', error);
            });
          }

          console.log(`📢 已通知全局用户标签页状态变化: ${eventType}`);
        } catch (error) {
          console.error('发送全局用户标签页状态变化通知失败:', error);
        }
      };

      // 立即发送一次
      await sendNotification();

      // 延迟再发送一次，确保所有操作完成
      setTimeout(sendNotification, 100); // 延迟100ms确保操作完成
    } catch (error) {
      console.error('通知全局用户标签页状态变化失败:', error);
    }
  }

  /**
   * 检查并执行数据迁移
   */
  private async checkAndMigrateData(): Promise<void> {
    try {
      console.log('🔍 检查数据迁移需求...');

      // 检测当前数据版本
      const versionResult = await MigrationManager.detectDataVersion();
      if (!versionResult.success) {
        console.error('检测数据版本失败:', versionResult.error);
        return;
      }

      const currentVersion = versionResult.data!;
      console.log(`📊 当前数据版本: ${currentVersion}`);

      // 如果是旧版本数据，执行迁移
      if (currentVersion !== '1.0.0') {
        console.log('🚀 开始执行数据迁移...');

        const migrationResult = await MigrationManager.migrateToWorkonaFormat({
          backupOriginalData: true,
          validateAfterMigration: true,
          rollbackOnError: true,
          preserveUserPreferences: true
        });

        if (migrationResult.success) {
          if (migrationResult.data) {
            console.log('✅ 基础数据迁移成功完成');

            // 概念性重构：标签页元数据迁移
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data! > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          } else {
            console.log('ℹ️ 数据已是最新版本，无需迁移');

            // 即使无需基础迁移，也检查元数据迁移
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data! > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          }
        } else {
          console.error('❌ 数据迁移失败:', migrationResult.error);
        }
      } else {
        console.log('✅ 数据版本已是最新，无需迁移');
      }
    } catch (error) {
      console.error('❌ 数据迁移检查过程中发生错误:', error);
    }
  }
}

// 初始化后台服务
new BackgroundService();
